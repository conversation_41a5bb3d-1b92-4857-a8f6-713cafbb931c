<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="drawerSize"
    :title="`${drawerProps.title} ${t('device.device')}`"
    @open="handleDrawerOpen"
    @close="() => onDrawerClose(resetTabState)"
  >
    <!-- 智能加载系统调试面板 -->
    <SmartLoadingDebugPanel
      :smart-loading-stats="smartLoadingStats"
      :last-update-time="lastUpdateTime"
      @update-smart-loading-stats="updateSmartLoadingStats"
      @clear-cache-and-update="clearCacheAndUpdate"
    />

    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="deviceConfig"
      :hide-required-asterisk="true"
      :validate-on-rule-change="false"
    >
      <el-form-item>
        <DeviceIcon :device-type="drawerProps.row?.deviceType" />
      </el-form-item>
      <!-- 设备名称编辑器 -->
      <DeviceNameEditor
        :device-config="deviceConfig"
        :drawer-props="drawerProps"
        :edit-name="editName"
        :device-name-changed="deviceNameChanged"
        @save-device-name="saveDeviceName(ruleFormRef)"
        @edit-device-name="editDeviceName"
        @cancel-edit="cancelEdit"
        @update:device-name-changed="deviceNameChanged = $event"
      />
      <el-tabs type="card" v-model="activeName" @tab-change="handleTabChange">
        <el-tab-pane :label="t('device.deviceInfo')" name="first">
          <DeviceInfo
            :device-id="drawerProps.row.deviceId"
            :device-model="drawerProps.row.deviceModel"
            :device-type="drawerProps.row.deviceType"
            :device-types="deviceTypes"
            :boot-time="drawerProps.row.bootTime"
            :mac="drawerProps.row.mac"
            :ipaddr="drawerProps.row.ipaddr"
            :status-tag-type="statusTagType"
            :status-label="statusLabel"
            :network="deviceStatus.network"
            :system="deviceStatus.system"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('device.networkSettings')" name="second">
          <el-collapse v-model="activeNames">
            <NetworkSettings
              :device-config="deviceConfig"
              :device-supports="drawerProps.row?.supports"
              :encryption-radio0-method="encryptionRadio0Method"
              :encryption-radio1-method="encryptionRadio1Method"
              :encryption-guest-method="encryptionGuestMethod"
              :encrypt-radio0="encryptRadio0"
              :encrypt-radio1="encryptRadio1"
              :encrypt-guest="encryptGuest"
              :guest-rate-options="guestRateOptions"
              @password-clear="handlePasswordClear"
              @update:encryption-radio0-method="encryptionRadio0Method = $event"
              @update:encryption-radio1-method="encryptionRadio1Method = $event"
              @update:encryption-guest-method="encryptionGuestMethod = $event"
              @guest-rate-change="handleGuestRateChange"
              @update:wifi-time-enabled="handleWifiTimeUpdate('enabled', $event)"
              @update:wifi-time-week="handleWifiTimeUpdate('week', $event)"
              @update:wifi-time-end-time="handleWifiTimeUpdate('endTime', $event)"
              @update:wifi-time-begin-time="handleWifiTimeUpdate('beginTime', $event)"
              @update:radio0-disabled="handleRadio0Update('disabled', $event)"
              @update:radio0-ssid="handleRadio0Update('ssid', $event)"
              @update:radio0-hidden="handleRadio0Update('hidden', $event)"
              @update:radio0-key="handleRadio0Update('key', $event)"
              @update:radio0-channel="handleRadio0Update('channel', $event)"
              @update:radio0-txpower="handleRadio0Update('txpower', $event)"
              @update:radio1-disabled="handleRadio1Update('disabled', $event)"
              @update:radio1-ssid="handleRadio1Update('ssid', $event)"
              @update:radio1-hidden="handleRadio1Update('hidden', $event)"
              @update:radio1-key="handleRadio1Update('key', $event)"
              @update:radio1-channel="handleRadio1Update('channel', $event)"
              @update:radio1-txpower="handleRadio1Update('txpower', $event)"
              @update:guest-disabled="handleGuestUpdate('disabled', $event)"
              @update:guest-ssid="handleGuestUpdate('ssid', $event)"
              @update:guest-hidden="handleGuestUpdate('hidden', $event)"
              @update:guest-key="handleGuestUpdate('key', $event)"
              @update:guest-rate="handleGuestUpdate('rate', $event)"
              @update:guest-wifi-time="handleGuestUpdate('wifiTime', $event)"
              @update:dhcp-enabled="handleDhcpUpdate('enabled', $event)"
              @update:dhcp-start="handleDhcpUpdate('start', $event)"
              @update:dhcp-end="handleDhcpUpdate('end', $event)"
              @update:dhcp-leasetime="handleDhcpUpdate('leasetime', $event)"
              @update:dhcp-gateway="handleDhcpUpdate('gateway', $event)"
              @update:dhcp-netmask="handleDhcpUpdate('netmask', $event)"
              @update:dhcp-dns1="handleDhcpUpdate('dns1', $event)"
              @update:dhcp-dns2="handleDhcpUpdate('dns2', $event)"
              @update:lan-ipaddr="handleLanUpdate('ipaddr', $event)"
              @update:lan-netmask="handleLanUpdate('netmask', $event)"
              @update:lan-gateway="handleLanUpdate('gateway', $event)"
              @update:lan-mtu="handleLanUpdate('mtu', $event)"
              @update:lan-stp="handleLanUpdate('stp', $event)"
              @update:lan-igmp-snooping="handleLanUpdate('igmp_snooping', $event)"
              @update:wan-proto="handleWanUpdate('proto', $event)"
              @update:wan-ipaddr="handleWanUpdate('ipaddr', $event)"
              @update:wan-netmask="handleWanUpdate('netmask', $event)"
              @update:wan-gateway="handleWanUpdate('gawa', $event)"
              @update:wan-username="handleWanUpdate('username', $event)"
              @update:wan-password="handleWanUpdate('password', $event)"
              @update:wan-mtu="handleWanUpdate('mtu', $event)"
              @update:wan-dns1="handleWanUpdate('dns1', $event)"
              @update:wan-dns2="handleWanUpdate('dns2', $event)"
              @update:dns-enabled="handleDnsUpdate('enabled', $event)"
              @update:dns-server1="handleDnsUpdate('server1', $event)"
              @update:dns-server2="handleDnsUpdate('server2', $event)"
              @update:dns-cache="handleDnsUpdate('cache', $event)"
              @update:dns-rebind-protection="handleDnsUpdate('rebindProtection', $event)"
              @update:dns-local-domain="handleDnsUpdate('localDomain', $event)"
              @add-dns-host="handleAddDnsHost"
              @remove-dns-host="handleRemoveDnsHost"
              @update:dns-hostname="handleDnsUpdate('hostname', $event)"
              @update:dns-host-ip="handleDnsUpdate('hostIp', $event)"
              @update:bridge-wifi-enabled="handleBridgeWifiUpdate('enabled', $event)"
              @update:bridge-wifi-ssid="handleBridgeWifiUpdate('ssid', $event)"
              @update:bridge-wifi-encryption="handleBridgeWifiUpdate('encryption', $event)"
              @update:bridge-wifi-key="handleBridgeWifiUpdate('key', $event)"
              @update:bridge-wifi-channel="handleBridgeWifiUpdate('channel', $event)"
              @update:bridge-wifi-bandwidth="handleBridgeWifiUpdate('bandwidth', $event)"
              @update:bridge-wifi-txpower="handleBridgeWifiUpdate('txpower', $event)"
              @update:bridge-wifi-hidden="handleBridgeWifiUpdate('hidden', $event)"
              @update:bridge-wifi-isolation="handleBridgeWifiUpdate('isolation', $event)"
            />
          </el-collapse>
        </el-tab-pane>
        <!-- 安全设置 -->
        <SecuritySettings
          :device-config="deviceConfig"
          :device-status="deviceStatus"
          :drawer-props="drawerProps"
          @update-system-password="handleSystemPasswordUpdate"
          @update-br-safe="handleBrSafeUpdate"
        />
        <el-tab-pane :label="t('device.portSettings')" name="fourth" v-if="shouldShowPortSettings">
          <PortSettingsTab
            :device-config="deviceConfig"
            :drawer-props="drawerProps"
            :port-settings-loading="portSettingsLoading"
            :show-port-example="showPortExample"
            :port-states="portStates"
            :selected-rows="selectedRows"
            :port-active-icon="portActiveIcon"
            :port-deactive-icon="portDeactiveIcon"
            :is-row-selected="isRowSelected"
            @toggle-row-selection="toggleRowSelection"
            @handle-describe-confirm="handleDescribeConfirm"
            @handle-describe-cancel="handleDescribeCancel"
            @handle-describe-edit="handleDescribeEdit"
            @toggle-port-example="togglePortExample"
            @open-port-dialog="openPortDialog"
            @handle-selection-change="handleSelectionChange"
          />
        </el-tab-pane>
        <el-tab-pane :label="t('device.systemSettings')" name="fifth">
          <SystemSettingsTab
            :device-config="deviceConfig"
            :progress-show="progressShow"
            :downloading-per="downloadingPer"
            :device-supports="drawerProps.row?.supports"
            @handle-reboot="handleReboot"
            @handle-upgrade="handleUpgrade"
            @update-led-mode="
              value => {
                deviceConfig.system.led.mode = value;
              }
            "
            @update-led-begin-time="
              value => {
                deviceConfig.system.led.beginTime = value;
              }
            "
            @update-led-end-time="
              value => {
                deviceConfig.system.led.endTime = value;
              }
            "
            @update-reboot-enabled="
              value => {
                deviceConfig.system.reboot.enabled = value;
              }
            "
            @update-reboot-week="
              value => {
                deviceConfig.system.reboot.week = Number(value);
              }
            "
            @update-reboot-time="
              value => {
                deviceConfig.system.reboot.time = value;
              }
            "
            @update-reboot-rate-delay="
              value => {
                deviceConfig.system.reboot.rateDelay = value;
              }
            "
          />
        </el-tab-pane>
        <el-tab-pane
          :label="t('device.deviceStatistics')"
          name="sixth"
          v-if="drawerProps.row?.supports?.system?.supports.includes('statistics')"
        >
          <DeviceStatisticsTab :chart-data="chartData" :chart-width="chartWidth" :chart-height="chartHeight" />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog()">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :disabled="!hasConfigChanged" @click="() => handleSubmit()">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
  <!-- 端口配置对话框 -->
  <PortConfigDialog
    v-model:visible="portDialogVisible"
    :device-config="deviceConfig"
    :selected-row-names="selectedRowNames"
    :sw-port="swPort"
    :sw-poe="swPoe"
    :sw-vlan="swVlan"
    :sw-storm="swStorm"
    :sw-qos="swQos"
    :speed-duplex-options="speedDuplexOptions"
    :poe-power-options="poePowerOptions"
    :vlan-mode-options="vlanModeOptions"
    :selected-traffic-types="selectedTrafficTypes"
    :traffic-type-options-array="trafficTypeOptionsArray"
    :rate-options="rateOptions"
    :isolate-rows="isolateRows"
    :sw-isolate="swIsolate"
    :permit-vlan-string="permitVlanString"
    :untag-vlan-string="untagVlanString"
    :dialog-tab-active="dialogTabActive"
    :has-dialog-config-changed="hasDialogConfigChanged"
    :port-active-icon="portActiveIcon"
    :port-deactive-icon="portDeactiveIcon"
    @update:permit-vlan-string="permitVlanString = $event"
    @update:untag-vlan-string="untagVlanString = $event"
    @update:dialog-tab-active="dialogTabActive = $event"
    @update-permit-vlan="updatePermitVlan"
    @update-untag-vlan="updateUntagVlan"
    @update-traffic-type="updateTrafficType"
    @handle-node-click="handleNodeClick"
    @is-port-selected="isPortSelected"
    @toggle-port-selection="togglePortSelection"
    @isolate-all="isolateAll"
    @get-port-names="getPortNames"
    @handle-submit="() => handleSubmit(true)"
  />
</template>

<script setup lang="ts" name="DeviceConfigDrawer">
import { computed, inject, onMounted, ref, watch } from "vue";
import DeviceIcon from "./DeviceIcon.vue";
import { useI18n } from "vue-i18n";
import { Project } from "@/api/interface/project";

import DeviceInfo from "./DeviceInfo.vue";
import NetworkSettings from "./DeviceConfigDrawer/NetworkSettings.vue";
import SmartLoadingDebugPanel from "./DeviceConfigDrawer/SmartLoadingDebugPanel.vue";
import DeviceNameEditor from "./DeviceConfigDrawer/DeviceNameEditor.vue";
import SecuritySettings from "./DeviceConfigDrawer/SecuritySettings.vue";
import PortConfigDialog from "./DeviceConfigDrawer/PortConfigDialog.vue";
import SystemSettingsTab from "./DeviceConfigDrawer/SystemSettingsTab.vue";
import PortSettingsTab from "./DeviceConfigDrawer/PortSettingsTab.vue";
import DeviceStatisticsTab from "./DeviceConfigDrawer/DeviceStatisticsTab.vue";

// 导入拆分的组合式函数
import { useDeviceConfig } from "./DeviceConfigDrawer/composables/useDeviceConfig";
import { useSmartLoading } from "./DeviceConfigDrawer/composables/useSmartLoading";
import { useTabLoading } from "./DeviceConfigDrawer/composables/useTabLoading";
import { useFormHandling } from "./DeviceConfigDrawer/composables/useFormHandling";
import { usePortConfig } from "./DeviceConfigDrawer/composables/usePortConfig";
import { useNetworkSettings } from "./DeviceConfigDrawer/composables/useNetworkSettings";

// 导入组合式函数和变量
import {
  // activeName, // 从组合式函数获取
  closeDialog,
  // deviceConfig, // 从组合式函数获取
  deviceNameChanged,
  deviceStatus,
  deviceWeekStatistic,
  downloadingPer,
  // drawerProps, // 从组合式函数获取
  // drawerVisible, // 从组合式函数获取
  editName,
  encryptGuest,
  encryptionGuestMethod,
  encryptionRadio0Method,
  encryptionRadio1Method,
  encryptRadio0,
  encryptRadio1,
  generatePortData,
  getPortNames,
  getPortStates,
  handleReboot,
  handleSelectionChange,
  handleUpgrade,
  isolateAll,
  isolateRows,
  isPortSelected,
  isRowSelected,
  // loadDeviceStatus, // 未使用
  poePowerOptions,
  // portDialogVisible, // 从组合式函数获取
  preloadRateOptions,
  progressShow,
  rateOptions,
  saveDeviceName,
  selectedRowNames,
  // selectedRows, // 从组合式函数获取
  selectedTrafficTypes,
  // showPortDialog, // 未使用
  // showPortExample, // 从组合式函数获取
  speedDuplexOptions,
  statusLabel,
  statusTagType,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan,
  togglePortExample,
  togglePortSelection,
  toggleRowSelection,
  trafficTypeOptionsArray,
  updateTrafficType,
  vlanModeOptions,
  // 添加导入缺失的变量
  manualEncryptRadio0,
  manualEncryptRadio1,
  manualEncryptGuest,
  originalKey0,
  originalKey1,
  originalKeyGuest
  // 按需加载功能现在由组合式函数提供
  // loadDataByTab,
  // loadDeviceStatusFields,
  // loadPortRelatedData,
  // loadSecurityData,
  // loadedTabs, // 导入 loadedTabs
  // clearLoadedTabs // 导入 clearLoadedTabs
  // deviceWeekStatistic 已在上面导入，不需要重复导入
} from "@/api/interface/deviceConfigDrawer";

// 导入智能加载系统 - 现在由组合式函数提供
// import {
//   smartLoadDeviceConfigFields,
//   smartLoadDeviceStatusFields,
//   preloadImportantData,
//   getLoadingState,
//   resetLoadingState
// } from "@/api/interface/deviceConfigDrawer/loadingManager";

// getLoadingStats, clearExpiredCache, clearAllCache 现在由 useSmartLoading 组合式函数提供
// import { getLoadingStats, clearExpiredCache, clearAllCache } from "@/api/interface/deviceConfigDrawer/smartLoader";

// 导入原始加载函数（用于降级）- 现在由组合式函数提供
// import {
//   loadDeviceConfigFields as originalLoadDeviceConfigFields,
//   loadDeviceStatusFields as originalLoadDeviceStatusFields
// } from "@/api/interface/deviceConfigDrawer_original";

// pushDeviceConfigJwe 现在由 useFormHandling 组合式函数提供
// import { pushDeviceConfigJwe } from "@/api/modules/project";
// useUserStore 现在由 useFormHandling 组合式函数提供
// import { useUserStore } from "@/stores/modules/user";

// stringifyWithoutEmpty 现在由 useFormHandling 组合式函数提供
// import { stringifyWithoutEmpty } from "@/utils";
// ECOption import removed - using D3 instead
// import { getDiff } from "@/utils/diff"; // 未使用
import { getDefaultDeviceConfig, getDefaultDeviceStatus } from "@/api/interface/deviceConfigDrawer";
// cloneDeep 现在由 useFormHandling 组合式函数提供
// import { cloneDeep } from "lodash";
import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";

const { t } = useI18n();

// 使用拆分的组合式函数
const { deviceConfig, originalData, drawerProps, drawerVisible, hasConfigChanged, acceptParams, onDrawerClose } =
  useDeviceConfig();

const { smartLoadingStats, lastUpdateTime, updateSmartLoadingStats, clearCacheAndUpdate, loadInitialData } = useSmartLoading();

const { tabLoadingState, loadTabData, loadDialogTabData, handleTabChange, clearLoadedTabs, resetTabState } =
  useTabLoading(deviceConfig);

const { formRefs, rules, handleSubmit, submitConfig } = useFormHandling(deviceConfig, drawerProps);
// handleSubmitAfterValidation 未使用

const { portConfig, openPortDialog, handleDescribeEdit, handleDescribeCancel, handleDescribeConfirm } = usePortConfig(
  deviceConfig,
  submitConfig,
  loadDialogTabData
);

const { eventHandlers } = useNetworkSettings(deviceConfig);

const baseGuestRateOptions = [
  { label: t("device.unlimitedSpeed"), value: 0 },
  { label: "8Mbps", value: 8 },
  { label: "16Mbps", value: 16 },
  { label: "32Mbps", value: 32 }
];
const guestRateOptions = ref([...baseGuestRateOptions]);

const portStates = getPortStates();

// 从组合式函数中解构需要的变量
const { activeName, activeNames } = tabLoadingState;
// const { loadedTabs } = tabLoadingState; // 未使用
const { selectedRows, portDialogVisible, dialogTabActive, showPortExample } = portConfig;
const { ruleFormRef } = formRefs;
// const { qosForm, vlanForm, portForm, poeForm, stormForm, isolationForm } = formRefs; // 未使用

// 从事件处理器中解构需要的函数
const {
  handlePasswordClear,
  handleGuestRateChange,
  handleWifiTimeUpdate,
  handleRadio0Update,
  handleRadio1Update,
  handleGuestUpdate,
  handleDhcpUpdate,
  handleLanUpdate,
  handleWanUpdate,
  handleDnsUpdate,
  handleBridgeWifiUpdate,
  handleSystemPasswordUpdate,
  handleBrSafeUpdate
} = eventHandlers;

// const password = ref(""); // 未使用

// 智能加载相关函数现在由 useSmartLoading 组合式函数提供

// onDrawerClose 和 handleTabChange 现在由组合式函数提供

// 字符串形式的 VLAN 值
const permitVlanString = ref("");
const untagVlanString = ref("");

// 更新 VLAN 数组
const updatePermitVlan = () => {
  if (!permitVlanString.value) {
    swVlan.value.permit = [];
    return;
  }

  swVlan.value.permit = permitVlanString.value
    .split(",")
    .map(item => {
      const num = parseInt(item.trim(), 10);
      return isNaN(num) ? 0 : num;
    })
    .filter(num => num > 0 && num <= 4094);
};

const updateUntagVlan = () => {
  if (!untagVlanString.value) {
    swVlan.value.untag = [];
    return;
  }

  const values = untagVlanString.value
    .split(",")
    .map(item => {
      const num = parseInt(item.trim(), 10);
      return isNaN(num) ? 0 : num;
    })
    .filter(num => num > 0 && num <= 4094);

  swVlan.value.untag = values;
};

// D3图表数据
const chartData = ref<Array<{ date: string; rxByte: number; txByte: number }>>([]);
const chartWidth = ref(640);
const chartHeight = ref(400);

// 异步更新图表数据
const updateChartData = () => {
  // 从deviceWeekStatistic中获取数据
  if (deviceWeekStatistic.value?.statistics) {
    // 转换数据格式为D3需要的格式
    chartData.value = deviceWeekStatistic.value.statistics.map(item => ({
      date: item.date,
      rxByte: item.rxByte,
      txByte: item.txByte
    }));
  }
};

// openPortDialog 函数现在由 usePortConfig 组合式函数提供

// 端口配置标签页的加载状态
const portSettingsLoading = ref(false);

// loadTabData 函数现在由 useTabLoading 组合式函数提供

// loadInitialData 函数现在由 useSmartLoading 组合式函数提供

// 兼容原有的合并加载函数
const handleDrawerOpen = async () => {
  console.log("🚪 抽屉打开事件触发");
  console.log("📋 drawerProps.row:", drawerProps.value.row);
  console.log("📋 deviceConfig 初始状态:", deviceConfig.value);

  // 立即更新智能加载状态显示
  updateSmartLoadingStats();

  // 清理过期缓存 - 现在由 useSmartLoading 组合式函数提供
  clearCacheAndUpdate();

  // 再次更新状态显示
  setTimeout(updateSmartLoadingStats, 100);

  // 加载初始数据
  const result = await loadInitialData();

  // 加载第一个标签页的数据
  console.log("🔄 抽屉打开，加载第一个标签页数据...");
  await loadTabData("first");

  console.log("📋 deviceConfig 加载后状态:", deviceConfig.value);

  return result;
};

// acceptParams 函数现在由 useDeviceConfig 组合式函数提供
// 重复的函数体已删除

// submitConfig 函数现在由 useFormHandling 组合式函数提供
// 重复的函数体已删除

// modifyDescribe 函数现在由 usePortConfig 组合式函数提供
// const modifyDescribe = async (row: any) => {
// 重复的函数体已删除

// handleSubmit 函数现在由 useFormHandling 组合式函数提供
// const handleSubmit = (isDialog = false) => {
// 重复的函数体已删除

// handleSubmitAfterValidation 函数现在由 useFormHandling 组合式函数提供
// const handleSubmitAfterValidation = async (isDialog = false) => {
// 重复的函数体已删除

// 存储设备类型列表
const deviceTypes = inject("deviceTypes", ref<Project.ResConfigList[]>([]));

// rules 现在由 useFormHandling 组合式函数提供
// const rules = computed(() => {
// const baseRules: any = {
// deviceId: [{ required: false, message: t("device.deviceIdPlaceholder") }],
// deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
// deviceType: [{ required: false, message: t("device.typePlaceholder") }],
// mac: [{ required: false, message: t("device.macPlaceholder") }],
// ip: [{ required: false, message: t("device.ipPlaceholder") }],
// 起始IP 必填且正整数 - 现在由 useFormHandling 提供
// "network.dhcp.start": [
//   {
//     validator: (rule, value, callback) => {
//       const val = value === undefined || value === null ? "" : value.toString().trim();
//       if (val === "") {
//         callback(new Error(t("common.startIpRequired")));
//       } else if (!/^[1-9]\d*$/.test(val)) {
//         callback(new Error(t("device.startIpPositiveInt")));
//       } else if (Number(val) < 2 || Number(val) > 254) {
//         callback(new Error(t("common.startIpRange")));
//       } else {
//         callback();
//       }
//     },
//     trigger: "blur"
//   }
// ],
// 所有验证规则现在由 useFormHandling 组合式函数提供
// };
// 所有验证规则内容已删除，现在由 useFormHandling 组合式函数提供
// 所有验证规则内容已删除，现在由 useFormHandling 组合式函数提供

// return baseRules;
// });

// chartRef removed - using D3 instead
// 监听 deviceWeekStatistic 数据变化并更新报表配置
watch(
  () => deviceWeekStatistic.value,
  (newVal: any) => {
    if (newVal && newVal.statistics) {
      updateChartData();
    }
  }
);

// hasConfigChanged 现在由 useDeviceConfig 组合式函数提供

// loadDialogTabData 函数现在由 useTabLoading 组合式函数提供

// 监听主抽屉标签页切换，按需加载数据
watch(activeName, async (newTab: string, oldTab: string) => {
  if (newTab !== oldTab && drawerVisible.value) {
    console.log(`主抽屉标签页从 ${oldTab} 切换到 ${newTab}`);
    await loadTabData(newTab);
  }
});

// 监听弹出层标签页切换，按需加载数据
watch(dialogTabActive, async (newTab: string, oldTab: string) => {
  if (newTab !== oldTab && portDialogVisible.value) {
    console.log(`弹出层标签页从 ${oldTab} 切换到 ${newTab}`);
    await loadDialogTabData(newTab);
  }
});

// 监听弹出层显示状态变化
watch(portDialogVisible, (newVal: boolean) => {
  if (!newVal) {
    // 弹出层关闭时，清理弹出层相关的标签页缓存 - 现在由组合式函数处理
    // const dialogTabKeys = Array.from(loadedTabs.value).filter(key => key.startsWith("dialog_"));
    // dialogTabKeys.forEach(key => loadedTabs.value.delete(key));
    console.log("弹出层关闭，已清理弹出层标签页缓存");
  }
});

// 监听抽屉关闭事件，清除原始数据
watch(drawerVisible, (newVal: boolean) => {
  if (!newVal) {
    // 当抽屉关闭时，清除原始数据和加密状态
    originalData.value = null;
    Object.assign(deviceConfig, getDefaultDeviceConfig(drawerProps.value.row?.supports));
    Object.assign(deviceStatus, getDefaultDeviceStatus(drawerProps.value.row?.supports));
    if (deviceWeekStatistic) {
      deviceWeekStatistic.value = null;
    }
    guestRateOptions.value = [...baseGuestRateOptions];

    // 重置加密状态
    encryptRadio0.value = false;
    encryptRadio1.value = false;
    encryptGuest.value = false;
    manualEncryptRadio0.value = false;
    manualEncryptRadio1.value = false;
    manualEncryptGuest.value = false;
    originalKey0.value = "";
    originalKey1.value = "";
    originalKeyGuest.value = "";

    // 重置已加载的标签页状态 - 现在由组合式函数处理
    clearLoadedTabs();
  }
});

watch(
  () => deviceConfig.wireless?.guest?.rate,
  (newRate: number | null | undefined, oldRate: number | null | undefined) => {
    // 移除旧的自定义选项
    if (oldRate !== null && oldRate !== undefined && !baseGuestRateOptions.some(opt => opt.value === oldRate)) {
      const index = guestRateOptions.value.findIndex(opt => opt.value === oldRate);
      if (index !== -1) {
        guestRateOptions.value.splice(index, 1);
      }
    }
    // 添加新的自定义选项
    if (newRate !== null && newRate !== undefined && !baseGuestRateOptions.some(opt => opt.value === newRate)) {
      if (!guestRateOptions.value.some(opt => opt.value === newRate)) {
        guestRateOptions.value.push({ label: `${newRate}Mbps`, value: newRate });
      }
    }
  },
  { immediate: true }
);

// 将handleNodeClick函数修改为处理范围节点展开
const handleNodeClick = data => {
  console.log("点击的节点:", data);

  // 如果是"更多选择..."节点
  if (data.value === "custom") {
    console.log("点击了更多选择...节点");

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 生成速率范围选项
      const ranges = [
        { min: 64, max: 1024, step: 64 },
        { min: 1024, max: 10240, step: 512 },
        { min: 10240, max: 102400, step: 2048 },
        { min: 102400, max: 1024000, step: 10240 }
      ];

      const rangeOptions = ranges.map(range => ({
        label: `${range.min} - ${range.max}`,
        value: `range-${range.min}-${range.max}`,
        children: [], // 使用空数组而不是null，确保显示展开图标
        isLeaf: false,
        hasChildren: true,
        range
      }));

      // 更新节点的子节点
      data.children = rangeOptions;

      // 强制刷新树
      treeKey.value++;
    }
  }

  // 如果是范围节点
  if (data.value && typeof data.value === "string" && data.value.startsWith("range-")) {
    console.log("点击了范围节点:", data.value);

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 从range-min-max格式中提取min和max
      const parts = data.value.split("-");
      if (parts.length === 3) {
        const min = parseInt(parts[1], 10);
        const max = parseInt(parts[2], 10);

        // 根据范围决定步长
        let step = 64;
        if (min >= 1024 && min < 10240) {
          step = 512;
        } else if (min >= 10240 && min < 102400) {
          step = 2048;
        } else if (min >= 102400) {
          step = 10240;
        }

        // 生成范围内的所有速率选项
        const values = [];
        for (let value = min; value <= max; value += step) {
          values.push({
            label: `${value}`,
            value: value,
            isLeaf: true
          });
        }

        // 更新节点的子节点
        data.children = values;

        // 强制刷新树
        treeKey.value++;
      }
    }
  }
};

// 添加一个变量来强制刷新树
const treeKey = ref(0);

// 初始化数据
onMounted(async () => {
  try {
    // 加载速率选项
    console.log("开始加载速率选项...");
    rateOptions.value = await preloadRateOptions();

    // 确保"更多选择..."节点正确配置
    const customNode = rateOptions.value.find(node => node.value === "custom");
    if (customNode) {
      // 确保节点有children属性（即使是空数组），这样会显示展开图标
      customNode.children = [];
      customNode.isLeaf = false;
      customNode.hasChildren = true;
    }

    // 设置默认值
    if (!swStorm.value.rate1) swStorm.value.rate1 = 64;
    if (!swStorm.value.rate2) swStorm.value.rate2 = 64;
    if (!swStorm.value.rate3) swStorm.value.rate3 = 64;

    console.log("组件挂载后的rateOptions状态:", rateOptions.value);
  } catch (error) {
    console.error("初始化速率选项时出错:", error);
  }
});

// 处理描述编辑
// handleDescribeEdit 函数现在由 usePortConfig 组合式函数提供

// handleDescribeCancel 函数现在由 usePortConfig 组合式函数提供

// handleDescribeConfirm 函数现在由 usePortConfig 组合式函数提供

// 获取 originalData 里当前 tab 的原始数据
function getOriginalDialogData() {
  if (!originalData.value) return {};
  switch (dialogTabActive.value) {
    case "first":
      return { system: { swPort: originalData.value.system?.swPort } };
    case "second":
      return { system: { swPoe: originalData.value.system?.swPoe } };
    case "third":
      return { network: { swVlan: originalData.value.network?.swVlan } };
    case "fourth":
      return { system: { swStorm: originalData.value.system?.swStorm } };
    case "fifth":
      return { network: { swIsolate: originalData.value.network?.swIsolate } };
    case "sixth":
      return { system: { swQos: originalData.value.system?.swQos } };
    default:
      return {};
  }
}

const hasDialogConfigChanged = computed(() => {
  // 如果对话框没有打开，返回 false
  if (!portDialogVisible.value) {
    return false;
  }

  if (!originalData.value || !deviceConfig) {
    // 静默返回 false，避免频繁的控制台输出
    return false;
  }

  let dialogData = null;
  let currentData = null;

  switch (dialogTabActive.value) {
    case "first":
      currentData = swPort.value;
      dialogData = { system: { swPort: generatePortData(currentData) } };
      break;
    case "second":
      currentData = swPoe.value;
      dialogData = { system: { swPoe: generatePortData(currentData) } };
      break;
    case "third":
      currentData = swVlan.value;
      dialogData = { network: { swVlan: generatePortData(currentData) } };
      break;
    case "fourth":
      currentData = swStorm.value;
      dialogData = { system: { swStorm: generatePortData(currentData) } };
      break;
    case "fifth":
      currentData = swIsolate.value;
      dialogData = { network: { swIsolate: generatePortData(currentData) } };
      break;
    case "sixth":
      currentData = swQos.value;
      dialogData = { system: { swQos: generatePortData(currentData) } };
      break;
    default:
      // 静默返回，避免频繁的控制台输出
      return false;
  }

  // 如果没有选中端口，直接比较当前数据与原始数据
  if (!selectedRows.value || selectedRows.value.length === 0) {
    let originalDataForTab = null;
    switch (dialogTabActive.value) {
      case "first":
        originalDataForTab = originalData.value.system?.swPort;
        break;
      case "second":
        originalDataForTab = originalData.value.system?.swPoe;
        break;
      case "third":
        originalDataForTab = originalData.value.network?.swVlan;
        break;
      case "fourth":
        originalDataForTab = originalData.value.system?.swStorm;
        break;
      case "fifth":
        originalDataForTab = originalData.value.network?.swIsolate;
        break;
      case "sixth":
        originalDataForTab = originalData.value.system?.swQos;
        break;
    }

    const currentDataStr = JSON.stringify(currentData);
    const originalDataStr = JSON.stringify(originalDataForTab);
    const hasChanged = currentDataStr !== originalDataStr;

    // 减少调试输出，只在有变化时输出
    if (hasChanged) {
      console.log("hasDialogConfigChanged: 检测到配置变化", {
        dialogTabActive: dialogTabActive.value,
        hasChanged
      });
    }

    return hasChanged;
  }

  const dialogDataStr = JSON.stringify(dialogData);
  const originalDialogDataStr = JSON.stringify(getOriginalDialogData());
  const hasChanged = dialogDataStr !== originalDialogDataStr;

  // 减少调试输出，只在有变化时输出
  if (hasChanged) {
    console.log("hasDialogConfigChanged: 检测到配置变化", {
      dialogTabActive: dialogTabActive.value,
      hasChanged
    });
  }

  return hasChanged;
});

// handlePasswordClear 函数现在由 useNetworkSettings 组合式函数提供

// handleGuestRateChange 函数现在由 useNetworkSettings 组合式函数提供

// 所有网络设置处理函数现在由 useNetworkSettings 组合式函数提供

// 所有网络配置处理函数现在由 useNetworkSettings 组合式函数提供

// 添加DNS主机处理函数
const handleAddDnsHost = () => {
  if (!deviceConfig.network?.dns) {
    if (!deviceConfig.network) (deviceConfig as any).network = {};
    (deviceConfig.network as any).dns = {};
  }
  if (!(deviceConfig.network.dns as any).hosts) {
    (deviceConfig.network.dns as any).hosts = [];
  }
  (deviceConfig.network.dns as any).hosts.push({ hostname: "", ip: "" });
};

const handleRemoveDnsHost = (index: number) => {
  if (deviceConfig.network?.dns?.hosts && Array.isArray(deviceConfig.network.dns.hosts)) {
    deviceConfig.network.dns.hosts.splice(index, 1);
  }
};

// handleBridgeWifiUpdate 函数现在由 useNetworkSettings 组合式函数提供

defineExpose({
  acceptParams
});

// Add function to initialize activeNames
const initializeActiveNames = () => {
  // 如果已经有展开的面板，不要重置
  if (activeNames.value.length > 0) {
    console.log(`activeNames 已有展开项，跳过重新初始化:`, activeNames.value);
    return;
  }

  const names: string[] = [];

  // 检查WiFi设置面板
  if (drawerProps.value?.row?.supports?.wireless) {
    names.push("wifi");
  }

  // 检查DHCP设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("dhcp") && deviceConfig.network?.dhcp) {
    names.push("dhcp");
  }

  // 检查网桥WiFi设置面板
  if (deviceConfig.network?.brAp && Object.keys(deviceConfig.network.brAp).length > 0) {
    names.push("brAp");
  }

  // 检查LAN设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("lan") && deviceConfig.network?.lan) {
    names.push("lan");
  }

  // 检查WAN设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("wan") && deviceConfig.network?.wan) {
    names.push("wan");
  }

  // 检查DNS设置面板
  if (drawerProps.value?.row?.supports?.network?.supports?.includes("dns") && deviceConfig.network?.dns) {
    names.push("dns");
  }

  // 修改：默认展开所有面板，而不是只展开第一个
  if (names.length > 0) {
    activeNames.value = names;
    console.log(`初始化 activeNames (全部展开):`, activeNames.value);
  }
};

// Watch for changes in relevant properties and update activeNames
watch(
  [
    () => drawerProps.value?.row?.supports?.wireless,
    () => drawerProps.value?.row?.supports?.network?.supports,
    () => deviceConfig.network?.dhcp,
    () => deviceConfig.network?.brAp
  ],
  () => {
    // 只在抽屉显示时才初始化，避免在数据加载过程中重复重置
    if (drawerVisible.value) {
      initializeActiveNames();
    }
  },
  { immediate: false } // 改为 false，避免立即执行
);

// 添加 watch 来监听加密方式的变化
watch(
  [encryptionRadio0Method, encryptionRadio1Method, encryptionGuestMethod],
  ([radio0, radio1, guest]) => {
    // 只在初始化时设置一次加密状态
    if (!manualEncryptRadio0.value) {
      encryptRadio0.value = radio0;
      manualEncryptRadio0.value = true;
    }
    if (!manualEncryptRadio1.value) {
      encryptRadio1.value = radio1;
      manualEncryptRadio1.value = true;
    }
    if (!manualEncryptGuest.value) {
      encryptGuest.value = guest;
      manualEncryptGuest.value = true;
    }
  },
  { immediate: true }
);

// 添加取消编辑的临时变量和函数
const tempDeviceName = ref("");

// 修改编辑设备名称的函数，增加保存原始值
const editDeviceName = () => {
  // 处理设备名称可能是对象的情况
  let deviceNameValue = deviceConfig.deviceName;
  if (deviceConfig.deviceName && typeof deviceConfig.deviceName === "object") {
    const deviceNameObj = deviceConfig.deviceName as any;
    deviceNameValue = deviceNameObj.value || deviceNameObj.name || deviceNameObj.text || "";
  }
  tempDeviceName.value = deviceNameValue;
  editName.value = true;
};

// 添加取消编辑的函数
const cancelEdit = () => {
  // 确保恢复的是字符串值
  deviceConfig.deviceName = tempDeviceName.value;
  editName.value = false;
  deviceNameChanged.value = false;
  // 清除校验错误信息
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate("deviceName");
  }
};

// 响应式drawer宽度
const drawerSize = computed(() => {
  if (window.innerWidth <= 320) return "100vw";
  if (window.innerWidth <= 480) return "98vw";
  if (window.innerWidth <= 768) return "90vw";
  if (window.innerWidth <= 1024) return "80vw";
  return "680px";
});

// 响应式图表尺寸
watch(
  () => [window.innerWidth],
  () => {
    chartWidth.value = window.innerWidth <= 480 ? window.innerWidth - 60 : 640;
    chartHeight.value = window.innerWidth <= 480 ? 300 : 400;
  },
  { immediate: true }
);

// 计算端口设置标签页是否应该显示
const shouldShowPortSettings = computed(() => {
  const deviceType = drawerProps.value?.row?.deviceType;
  const supports = drawerProps.value?.row?.supports;
  const hasSwPoe = supports?.system?.supports?.includes("swPoe");
  const isSwitch = deviceType === "switch";
  const shouldShow = isSwitch && hasSwPoe;

  console.log("🔍 端口设置标签页显示条件检查:");
  console.log("- deviceType:", deviceType);
  console.log("- isSwitch:", isSwitch);
  console.log("- supports:", supports);
  console.log("- system.supports:", supports?.system?.supports);
  console.log("- hasSwPoe:", hasSwPoe);
  console.log("- shouldShow:", shouldShow);

  return shouldShow;
});

// 安全设置事件处理函数现在由 useNetworkSettings 组合式函数提供
</script>

<style lang="scss" scoped>
// 导入样式文件
@import "./DeviceConfigDrawer/styles/index.scss";
</style>
