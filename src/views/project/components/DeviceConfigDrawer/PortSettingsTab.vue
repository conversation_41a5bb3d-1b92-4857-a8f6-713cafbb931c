<template>
  <div>
    <div v-if="portSettingsLoading" style="padding: 16px">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else>
      <el-divider v-if="deviceConfig.system.swPort && drawerProps.row?.deviceType === 'switch'"></el-divider>
      <el-card
        v-if="drawerProps.row?.deviceType === 'switch' && deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0"
      >
        <ul class="sw-port-list">
          <li
            v-for="(item, index) in deviceConfig.system.swPort"
            :key="index"
            :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
            @click="$emit('toggleRowSelection', item)"
          >
            <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon" alt="Port Icon" />
            <div class="port-name-container">
              <el-input
                v-if="item.isEditing"
                v-model="item.describe"
                size="small"
                ref="portNameInput"
                @blur="$emit('handleDescribeConfirm', item)"
                @keyup.enter="$emit('handleDescribeConfirm', item)"
                @keyup.esc="$emit('handleDescribeCancel', item)"
                maxlength="32"
                show-word-limit
              ></el-input>
              <div v-else class="port-name-display" @click.stop="$emit('handleDescribeEdit', item)">
                <el-text class="port-name-text">{{ item.describe || item.name }}</el-text>
                <el-icon class="edit-icon"><Edit /></el-icon>
              </div>
            </div>
          </li>
        </ul>
        <el-button link :icon="showPortExample ? 'ArrowUp' : 'ArrowDown'" @click="$emit('togglePortExample')">{{
          showPortExample ? $t("common.CollapseDiagram") : $t("common.ExpandDiagram")
        }}</el-button>
        <div class="port-example-container" v-if="showPortExample">
          <div v-for="(item, index) in portStates" :key="index" class="sw-port-example">
            <el-image :src="item.icon" class="port-example-icon" :alt="item.text" />
            <span class="port-text">{{ item.text }}</span>
          </div>
        </div>
      </el-card>
      <el-divider></el-divider>
      <el-button size="default" type="primary" @click="$emit('openPortDialog')">{{ $t("device.configuration") }}</el-button>
      <el-table
        ref="portTableRef"
        :data="deviceConfig.system.swPort"
        style="width: 100%"
        @selection-change="$emit('handleSelectionChange', $event)"
        :selection="selectedRows"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column :label="t('device.portName')">
          <template #default="scope">
            <el-input
              v-if="scope.row.isEditing"
              v-model="scope.row.describe"
              ref="tablePortNameInput"
              @blur="$emit('handleDescribeConfirm', scope.row)"
              @keyup.enter="$emit('handleDescribeConfirm', scope.row)"
              @keyup.esc="$emit('handleDescribeCancel', scope.row)"
              maxlength="32"
              show-word-limit
            >
            </el-input>
            <div v-else class="edit-cell" @click="$emit('handleDescribeEdit', scope.row)">
              {{ scope.row.describe || scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="portenable" :label="t('device.portStatus')">
          <template #default="scope">
            <span>{{ scope.row.portenable === 1 ? t("device.open") : t("device.close") }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('device.portPowerLimit')" width="180px">
          <template #default="scope">
            <span>{{ deviceConfig.system?.swPoe[scope.$index]?.power ?? "--" }}/</span>
            {{
              deviceConfig.system.swPoe[scope.$index]?.powerout === 0
                ? "af (15.4w)"
                : deviceConfig.system.swPoe[scope.$index]?.powerout === 1
                  ? "at (30w)"
                  : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="power" :label="t('device.portRate')">
          <template #default="scope">
            {{ scope.row.link === 1 ? formatSpeedDuplex(scope.row.speed_duplex) : formatAutoneg(scope.row.autoneg) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts" name="PortSettingsTab">
import { useI18n } from "vue-i18n";
import { Edit } from "@element-plus/icons-vue";
import { formatSpeedDuplex, formatAutoneg } from "@/api/interface/device/formatter";

const { t } = useI18n();

// Props
defineProps<{
  deviceConfig: any;
  drawerProps: any;
  portSettingsLoading: boolean;
  showPortExample: boolean;
  portStates: any[];
  selectedRows: any[];
  portActiveIcon: string;
  portDeactiveIcon: string;
  isRowSelected: (item: any) => boolean;
}>();

// Emits
defineEmits<{
  toggleRowSelection: [item: any];
  handleDescribeConfirm: [item: any];
  handleDescribeCancel: [item: any];
  handleDescribeEdit: [item: any];
  togglePortExample: [];
  openPortDialog: [];
  handleSelectionChange: [selection: any[]];
}>();
</script>

<style lang="scss" scoped>
// 端口列表样式
.sw-port-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.sw-port-tag {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 65px;
  padding: 5px;
  margin: 1px;
  background-color: #f9f9f9;
  border: 1px solid transparent;
  border-radius: 5px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.sw-port-tag:hover {
  border-color: #91d5ff;
}

.sw-port-tag.selected-port {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 5px;
}

// 端口图标样式
.port-icon {
  width: 35px;
  height: 35px;
  flex-shrink: 0;
}

// 端口名称容器
.port-name-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 4px;
  margin-top: 2px;

  .port-name-display {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 1px 2px;
    border-radius: 2px;
    transition: background-color 0.3s;

    .port-name-text {
      font-size: 12px;
      line-height: 1.2;
      text-align: center;
      word-break: break-all;
      max-width: 40px;
    }

    .edit-icon {
      margin-left: 2px;
      font-size: 10px;
      visibility: hidden;
      opacity: 0.6;
    }

    &:hover {
      background-color: #f5f7fa;

      .edit-icon {
        visibility: visible;
      }
    }
  }

  .el-input {
    width: 100%;

    :deep(.el-input__inner) {
      font-size: 11px;
      text-align: center;
      padding: 2px 4px;
    }
  }
}

// 端口示例容器
.port-example-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}

.sw-port-example {
  display: inline-flex;
  align-items: center;
  font-size: 14px;

  .port-example-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .port-text {
    line-height: 24px;
    white-space: nowrap;
  }
}

// 表格编辑单元格
.edit-cell {
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
