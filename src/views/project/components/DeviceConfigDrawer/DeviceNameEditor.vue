<template>
  <el-form-item :label="$t('device.deviceName')" label-width="160px" prop="deviceName">
    <el-text v-if="!editName">{{ displayDeviceName }}</el-text>
    <el-input
      v-model="deviceConfig.deviceName"
      v-if="editName"
      clearable
      style="width: 60%"
      :placeholder="t('device.deviceNamePlaceholder')"
      @input="handleInput"
    >
      <template #append>
        <div style="display: flex; gap: 8px; align-items: center">
          <el-link
            type="danger"
            :icon="Close"
            @click="cancelEdit"
            v-if="editName"
            style="display: flex; align-items: center; justify-content: center; height: 20px; padding: 0"
          />
          <el-divider direction="vertical" style="margin: 0" />
          <el-link
            type="primary"
            :icon="Check"
            @click="saveDeviceName"
            v-if="editName"
            :disabled="!deviceNameChanged"
            style="display: flex; align-items: center; justify-content: center; height: 20px; padding: 0"
          />
        </div>
      </template>
    </el-input>
    <el-link target="_blank" :icon="Edit" @click="editDeviceName" v-if="!editName">
      {{ $t("common.edit") }}
    </el-link>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { Check, Edit, Close } from "@element-plus/icons-vue";

// Props
interface Props {
  deviceConfig: {
    deviceName?: string;
    [key: string]: any;
  };
  drawerProps: {
    row: {
      deviceName?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };
  editName: boolean;
  deviceNameChanged: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "update:deviceConfig": [value: any];
  "update:editName": [value: boolean];
  "update:deviceNameChanged": [value: boolean];
  saveDeviceName: [];
  editDeviceName: [];
  cancelEdit: [];
}>();

// Composables
const { t } = useI18n();

// Computed
const displayDeviceName = computed(() => {
  return props.deviceConfig.deviceName || props.drawerProps.row?.deviceName || t("device.unknownDevice");
});

// Methods
const editDeviceName = () => {
  emit("editDeviceName");
};

const cancelEdit = () => {
  emit("cancelEdit");
};

const saveDeviceName = () => {
  emit("saveDeviceName");
};

const handleInput = () => {
  emit("update:deviceNameChanged", true);
};
</script>

<style lang="scss" scoped>
// 设备名称编辑器样式
</style>
