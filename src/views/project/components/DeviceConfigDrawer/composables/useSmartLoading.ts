import { ref, onUnmounted } from "vue";
import {
  smartLoadDeviceConfigFields,
  preloadImportantData,
  getLoadingState,
  resetLoadingState
} from "@/api/interface/deviceConfigDrawer/loadingManager";
import { getLoadingStats, clearExpiredCache, clearAllCache } from "@/api/interface/deviceConfigDrawer/smartLoader";
import type { UseSmartLoadingReturn, SmartLoadingStats } from "../types";

export function useSmartLoading(): UseSmartLoadingReturn {
  // 智能加载系统状态监控
  const isDevelopment = ref(process.env.NODE_ENV === "development");
  const smartLoadingStats = ref<SmartLoadingStats>({
    cacheSize: 0,
    loadedFields: [],
    failedFields: [],
    isLoading: false
  });
  const lastUpdateTime = ref("未更新");

  // 定时器引用
  let smartLoadingStatsTimer: NodeJS.Timeout | null = null;

  // 更新智能加载状态的函数
  const updateSmartLoadingStats = () => {
    try {
      const stats = getLoadingStats();
      const state = getLoadingState();
      smartLoadingStats.value = {
        cacheSize: stats.cacheSize,
        loadedFields: stats.cacheKeys || [], // 使用 cacheKeys 作为已加载字段
        failedFields: [], // 暂时设为空数组，后续可以从 state 中获取
        isLoading: state.isLoading
      };
      lastUpdateTime.value = new Date().toLocaleTimeString();
    } catch (error) {
      console.warn("更新智能加载状态失败:", error);
    }
  };

  // 清理缓存并更新状态
  const clearCacheAndUpdate = () => {
    try {
      clearAllCache(); // 强制清理所有缓存
      console.log("🧹 手动清理所有缓存完成");
      updateSmartLoadingStats();
    } catch (error) {
      console.error("清理缓存失败:", error);
    }
  };

  // 启动定时更新状态（仅在开发环境）
  if (isDevelopment.value) {
    smartLoadingStatsTimer = setInterval(updateSmartLoadingStats, 2000);
  }

  // 初始加载必要的基础数据 - 使用智能加载系统
  const loadInitialData = async (): Promise<boolean> => {
    try {
      // 清理过期缓存
      clearExpiredCache();

      // 重置加载状态（如果需要）
      resetLoadingState();

      console.log("🚀 开始智能加载基础配置字段...");

      // 使用智能加载系统加载基础配置字段
      await smartLoadDeviceConfigFields(["deviceName", "deviceId", "deviceModel", "deviceType", "mac", "ipaddr"], {
        priority: "high",
        useCache: true,
        timeout: 15000
      });

      console.log("✅ 智能加载基础配置字段完成");
      // 立即更新状态显示
      updateSmartLoadingStats();

      // 预加载重要数据（异步，不阻塞界面）
      preloadImportantData().catch(error => {
        console.warn("⚠️ 预加载重要数据失败:", error);
      });

      return true;
    } catch (error) {
      console.error("❌ 智能加载基础数据失败:", error);

      // 更新失败状态
      updateSmartLoadingStats();

      return false;
    }
  };

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (smartLoadingStatsTimer) {
      clearInterval(smartLoadingStatsTimer);
    }
  });

  return {
    smartLoadingStats,
    lastUpdateTime,
    updateSmartLoadingStats,
    clearCacheAndUpdate,
    loadInitialData
  };
}
