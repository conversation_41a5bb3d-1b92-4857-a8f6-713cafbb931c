import { ref } from "vue";
import type { UseNetworkSettingsReturn, NetworkSettings, EventHandlers } from "../types";

export function useNetworkSettings(deviceConfig: any): UseNetworkSettingsReturn {
  // 网络设置状态
  const networkSettings: NetworkSettings = {
    encryptionRadio0Method: ref(false),
    encryptionRadio1Method: ref(false),
    encryptionGuestMethod: ref(false),
    encryptRadio0: ref(false),
    encryptRadio1: ref(false),
    encryptGuest: ref(false)
  };

  // 事件处理器
  const eventHandlers: EventHandlers = {
    // 处理密码清空
    handlePasswordClear: (type: "radio0" | "radio1" | "guest") => {
      if (!deviceConfig.wireless?.[type]) return;
      deviceConfig.wireless[type].key = "";
    },

    // 处理访客WiFi速率变化
    handleGuestRateChange: (value: string | number) => {
      if (!deviceConfig.wireless?.guest) return;

      const numValue = typeof value === "string" ? parseInt(value) : value;
      if (!isNaN(numValue)) {
        deviceConfig.wireless.guest.rate = numValue;
      }
    },

    // WiFi定时设置处理
    handleWifiTimeUpdate: (field: string, value: any) => {
      if (!deviceConfig.wireless?.wifiTime) {
        if (!deviceConfig.wireless) (deviceConfig as any).wireless = {};
        (deviceConfig.wireless as any).wifiTime = {};
      }
      deviceConfig.wireless.wifiTime[field] = value;
    },

    // Radio0设置处理
    handleRadio0Update: (field: string, value: any) => {
      if (!deviceConfig.wireless?.radio0) return;
      deviceConfig.wireless.radio0[field] = value;
    },

    // Radio1设置处理
    handleRadio1Update: (field: string, value: any) => {
      if (!deviceConfig.wireless?.radio1) return;
      deviceConfig.wireless.radio1[field] = value;
    },

    // Guest设置处理
    handleGuestUpdate: (field: string, value: any) => {
      if (!deviceConfig.wireless?.guest) return;
      deviceConfig.wireless.guest[field] = value;
    },

    // DHCP设置处理
    handleDhcpUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.dhcp) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).dhcp = {};
      }
      (deviceConfig.network.dhcp as any)[field] = value;
    },

    // LAN设置处理
    handleLanUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.lan) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).lan = {};
      }
      deviceConfig.network.lan[field] = value;
    },

    // WAN设置处理
    handleWanUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.wan || !Array.isArray(deviceConfig.network.wan) || deviceConfig.network.wan.length === 0) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).wan = [{}];
      }
      deviceConfig.network.wan[0][field] = value;
    },

    // DNS设置处理
    handleDnsUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.dns) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).dns = {};
      }
      (deviceConfig.network.dns as any)[field] = value;
    },

    // 桥接WiFi设置处理
    handleBridgeWifiUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.brAp) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).brAp = {};
      }
      (deviceConfig.network.brAp as any)[field] = value;
    },

    // 系统密码设置处理
    handleSystemPasswordUpdate: (field: string, value: string) => {
      if (!deviceConfig.system) {
        (deviceConfig as any).system = {};
      }
      (deviceConfig.system as any)[field] = value;
    },

    // 桥接安全设置处理
    handleBrSafeUpdate: (field: string, value: any) => {
      if (!deviceConfig.network?.brSafe) {
        if (!deviceConfig.network) (deviceConfig as any).network = {};
        (deviceConfig.network as any).brSafe = {};
      }
      (deviceConfig.network.brSafe as any)[field] = value;
    }
  };

  return {
    networkSettings,
    eventHandlers
  };
}
