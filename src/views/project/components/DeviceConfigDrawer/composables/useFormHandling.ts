import { ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { stringifyWithoutEmpty } from "@/utils";
import type { UseFormHandlingReturn, FormRefs } from "../types";
import { Project } from "@/api/interface/project";

export function useFormHandling(deviceConfig: any, drawerProps: any): UseFormHandlingReturn {
  const { t } = useI18n();
  const userStore = useUserStore();
  // 表单引用
  const formRefs: FormRefs = {
    ruleFormRef: ref<FormInstance>(),
    qosForm: ref<FormInstance>(),
    vlanForm: ref<FormInstance>(),
    portForm: ref<FormInstance>(),
    poeForm: ref<FormInstance>(),
    stormForm: ref<FormInstance>(),
    isolationForm: ref<FormInstance>()
  };

  // 表单验证规则
  const rules = computed(() => {
    const baseRules: any = {
      deviceId: [{ required: false, message: t("device.deviceIdPlaceholder") }],
      deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
      deviceType: [{ required: false, message: t("device.typePlaceholder") }],
      mac: [{ required: false, message: t("device.macPlaceholder") }],
      ipaddr: [{ required: false, message: t("device.ipPlaceholder") }],
      deviceName: [
        { required: true, message: t("device.deviceNameRequired"), trigger: "blur" },
        { min: 1, max: 32, message: t("device.deviceNameLength"), trigger: "blur" }
      ]
    };

    // 动态添加SSID校验规则
    const hasWirelessSupport = drawerProps.value.row?.supports?.wireless;
    if (hasWirelessSupport) {
      if (deviceConfig.wireless?.radio0 && Object.keys(deviceConfig.wireless.radio0).length > 0) {
        baseRules["wireless.radio0.ssid"] = [
          { required: true, message: t("device.ssidRequired"), trigger: "blur" },
          { min: 1, max: 32, message: t("device.ssidLength"), trigger: "blur" }
        ];
      }
      if (deviceConfig.wireless?.radio1 && Object.keys(deviceConfig.wireless.radio1).length > 0) {
        baseRules["wireless.radio1.ssid"] = [
          { required: true, message: t("device.ssidRequired"), trigger: "blur" },
          { min: 1, max: 32, message: t("device.ssidLength"), trigger: "blur" }
        ];
      }
    }

    return baseRules;
  });

  // 通用的提交配置函数
  const submitConfig = async (configData: any): Promise<boolean> => {
    try {
      // 提交前移除 reSysPassword 字段
      if (configData.system && configData.system.reSysPassword !== undefined) {
        delete configData.system.reSysPassword;
      }

      console.log("📤 准备提交配置数据:", stringifyWithoutEmpty(configData));

      // 构建提交参数 - 符合 ReqConfigParams 接口
      const params: Project.ReqConfigParams = {
        cmd: 1, // 配置命令
        deviceId: drawerProps.value.row.deviceId,
        userId: userStore.userInfo.userId || "system",
        data: configData
      };

      console.log("📤 提交参数:", stringifyWithoutEmpty(params));

      const result = await pushDeviceConfigJwe(params);

      if (result.code === "200") {
        ElMessage.success(t("common.updateSuccess"));
        console.log("✅ 配置提交成功");
        return true;
      } else {
        const errorMsg = result.msg || t("common.updateFailed");
        ElMessage.error(errorMsg);
        console.error("❌ 配置提交失败:", result);
        return false;
      }
    } catch (error) {
      console.error("❌ 提交配置时发生错误:", error);
      ElMessage.error(t("common.updateFailed"));
      return false;
    }
  };

  // 提交数据验证后的处理逻辑
  const handleSubmitAfterValidation = async (isDialog = false): Promise<boolean> => {
    try {
      // SSID 必填校验
      const hasWirelessSupport = drawerProps.value.row?.supports?.wireless;
      const needCheckRadio0 =
        hasWirelessSupport && deviceConfig.wireless?.radio0 && Object.keys(deviceConfig.wireless.radio0).length > 0;
      const needCheckRadio1 =
        hasWirelessSupport && deviceConfig.wireless?.radio1 && Object.keys(deviceConfig.wireless.radio1).length > 0;

      if (needCheckRadio0 && (!deviceConfig.wireless.radio0.ssid || deviceConfig.wireless.radio0.ssid.trim() === "")) {
        ElMessage.error(t("device.radio0SsidRequired"));
        return false;
      }

      if (needCheckRadio1 && (!deviceConfig.wireless.radio1.ssid || deviceConfig.wireless.radio1.ssid.trim() === "")) {
        ElMessage.error(t("device.radio1SsidRequired"));
        return false;
      }

      // 密码确认校验
      if (deviceConfig.system?.sysPassword && deviceConfig.system?.reSysPassword) {
        if (deviceConfig.system.sysPassword !== deviceConfig.system.reSysPassword) {
          ElMessage.error(t("device.passwordMismatch"));
          return false;
        }
      }

      // 根据是否为对话框提交决定提交的数据
      let configToSubmit;
      if (isDialog) {
        // 对话框提交：只提交相关的配置部分
        configToSubmit = { system: {} };

        // 根据当前活跃的对话框标签页决定提交哪些数据
        // 这里需要根据实际的对话框标签页来决定
        configToSubmit.system = { swPort: deviceConfig.system?.swPort || [] };
      } else {
        // 主表单提交：提交完整配置
        configToSubmit = deviceConfig;
      }

      return await submitConfig(configToSubmit);
    } catch (error) {
      console.error("❌ 表单提交验证失败:", error);
      ElMessage.error(t("common.updateFailed"));
      return false;
    }
  };

  // 提交数据（新增/编辑）
  const handleSubmit = (isDialog = false) => {
    // 如果是对话框提交，使用对应的表单引用进行验证
    let formRef = formRefs.ruleFormRef;

    if (isDialog) {
      // 根据对话框标签页选择对应的表单引用
      // 这里可以根据实际需要扩展
      formRef = formRefs.portForm;
    }

    if (!formRef.value) {
      console.error("表单引用不存在");
      return;
    }

    formRef.value.validate(async (valid: boolean) => {
      if (valid) {
        await handleSubmitAfterValidation(isDialog);
      } else {
        console.log("表单验证失败");
      }
    });
  };

  return {
    formRefs,
    rules,
    handleSubmit,
    handleSubmitAfterValidation,
    submitConfig
  };
}
